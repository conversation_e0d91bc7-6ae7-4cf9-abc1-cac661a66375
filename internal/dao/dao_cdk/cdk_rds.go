package dao_cdk

import (
	"context"
	"encoding/json"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"hallsrv/internal/config"
	"hallsrv/internal/model/model_cdk"
)

func GetRedisCli() *redisx.Client {
	return redisx.GetGeneralCli()
}

// GetCdkInfoFromCache 从缓存中获取CDK信息
func GetCdkInfoFromCache(ctx context.Context, cdkCode string) (*model_cdk.CdkBatchRecordResult, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("GetCdkInfoFromCache: trying to get CDK info from cache for code: %s", cdkCode)

	key := config.CdkInfoKey(cdkCode)
	data, err := GetRedisCli().Get(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	// 解析缓存数据
	var result model_cdk.CdkBatchRecordResult
	err = json.Unmarshal([]byte(data), &result)
	if err != nil {
		return nil, err
	}

	entry.Debugf("GetCdkInfoFromCache: successfully retrieved CDK info from cache for code: %s", cdkCode)
	return &result, nil
}

// SetCdkInfoToCache 将CDK信息存入缓存
func SetCdkInfoToCache(ctx context.Context, cdkCode string, info *model_cdk.CdkBatchRecordResult) error {
	if info == nil {
		return nil // 不缓存空数据
	}

	entry := logx.NewLogEntry(ctx)
	entry.Debugf("SetCdkInfoToCache: setting CDK info to cache for code: %s", cdkCode)

	// 序列化数据
	data, err := json.Marshal(info)
	if err != nil {
		entry.Errorf("SetCdkInfoToCache: failed to marshal CDK info for code %s: %v", cdkCode, err)
		return err
	}

	// 存入缓存
	key := config.CdkInfoKey(cdkCode)
	err = GetRedisCli().Set(ctx, key, string(data), config.CDK_CACHE_EXPIRE).Err()
	if err != nil {
		entry.Errorf("SetCdkInfoToCache: failed to set CDK info to cache for code %s: %v", cdkCode, err)
		return err
	}

	entry.Debugf("SetCdkInfoToCache: successfully set CDK info to cache for code: %s", cdkCode)
	return nil
}

// DeleteCdkInfoFromCache 从缓存中删除CDK信息
func DeleteCdkInfoFromCache(ctx context.Context, cdkCode string) error {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("DeleteCdkInfoFromCache: deleting CDK info from cache for code: %s", cdkCode)

	key := config.CdkInfoKey(cdkCode)
	_, err := GetRedisCli().Del(ctx, key).Result()
	if err != nil {
		entry.Errorf("DeleteCdkInfoFromCache: failed to delete CDK info from cache for code %s: %v", cdkCode, err)
		return err
	}

	entry.Debugf("DeleteCdkInfoFromCache: successfully deleted CDK info from cache for code: %s", cdkCode)
	return nil
}
