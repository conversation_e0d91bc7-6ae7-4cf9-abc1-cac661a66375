package model_cdk

import (
	"encoding/json"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"github.com/sirupsen/logrus"
)

// CDKGenerationOption 定义了CDK码的生成方式类型
// CDKGenerationOption defines the type for CDK code generation methods
type CDKGenerationOption int

const (
	_ CDKGenerationOption = iota // Skip 0 for default/uninitialized
	// RandomGeneration 表示随机生成CDK码
	RandomGeneration
	// ManualInput 表示手动输入CDK码
	ManualInput
)

// CreateCDKBatchInput 是后台创建CDK批次和对应CDK码的输入参数结构体。
// CreateCDKBatchInput is the input parameter structure for creating CDK batches and corresponding CDK codes in the backend.
type CreateCDKBatchInput struct {
	ChannelID        int32                `json:"channel_id" binding:"required"`        // 渠道ID (必填)
	Description      string               `json:"description,omitempty"`                // 批次描述 (可选)
	GenerationOption CDKGenerationOption  `json:"generation_option" binding:"required"` // CDK生成方式
	GenerationCount  int                  `json:"generation_count,omitempty"`           // 随机生成时，需要生成的CDK数量 (当 generation_option 为 "random" 时必填, >0)
	ManualCDKs       []string             `json:"manual_cdks,omitempty"`                // 手动输入时，指定的CDK码列表 (当 generation_option 为 "manual" 时必填, 不能为空数组)
	StartTime        int64                `json:"start_time" binding:"required"`        // 生效开始时间 (Unix时间戳, 秒) (必填)
	EndTime          int64                `json:"end_time" binding:"required"`          // 生效结束时间 (Unix时间戳, 秒) (必填, 必须大于 start_time)
	CDKUseLimit      int32                `json:"cdk_use_limit" binding:"min=0"`        // 单个CDK可被所有用户使用的总次数 (-1表示不限制，默认为0)
	Rewards          []*commonPB.ItemBase `json:"rewards" binding:"required,dive"`      // CDK奖励列表 (必填, 不能为空数组)
}

// CreateCDKBatchOutput 是后台创建CDK批次操作的输出结果。
type CreateCDKBatchOutput struct {
	Batch *CDKBatch `json:"batch"` // 创建成功的CDK批次信息
}

func (c *CreateCDKBatchInput) GetRewardsStr() string {
	if c == nil {
		return ""
	}
	jsonRewards, marshalErr := json.Marshal(c.Rewards)
	if marshalErr != nil {
		logrus.Errorf("failed to marshal rewards: %s", marshalErr)
		return ""
	}
	return string(jsonRewards)
}

// ToCDKBatch 将输入参数转换为CDKBatch对象
func (c *CreateCDKBatchInput) ToCDKBatch() *CDKBatch {
	if c == nil {
		return nil
	}

	startTime := time.Unix(c.StartTime, 0)
	endTime := time.Unix(c.EndTime, 0)
	now := time.Now()

	return &CDKBatch{
		ChannelID:        c.ChannelID,
		Description:      c.Description,
		GenerationOption: int8(c.GenerationOption),
		StartTime:        startTime,
		EndTime:          endTime,
		CDKLimit:         c.CDKUseLimit,
		Rewards:          c.GetRewardsStr(),
		Status:           CDKStatusValid, // 默认为有效状态
		CreatedAt:        now,
		UpdatedAt:        now,
	}
}

// CreateCDKRecords 根据CDK码列表创建CDKRecord对象列表
func CreateCDKRecords(cdkCodes []string) []*CDKRecord {
	if len(cdkCodes) == 0 {
		return nil
	}

	now := time.Now()
	records := make([]*CDKRecord, 0, len(cdkCodes))

	for _, code := range cdkCodes {
		record := &CDKRecord{
			CDK:       code,
			CreatedAt: now,
			UpdatedAt: now,
		}
		records = append(records, record)
	}

	return records
}
