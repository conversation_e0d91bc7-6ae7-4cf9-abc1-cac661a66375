package logic_cdk

import (
	"context"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"github.com/stretchr/testify/assert"
	"hallsrv/internal/model/model_cdk"
	"hallsrv/internal/test"
	"testing"
	"time"
)

func TestCreateCDKBatchWithCodes(t *testing.T) {
	test.Init()
	type args struct {
		ctx   context.Context
		input *model_cdk.CreateCDKBatchInput
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		checkFn func(t *testing.T, output *model_cdk.CreateCDKBatchOutput, err error)
	}{
		{
			name: "Success - Random Generation",
			args: args{
				ctx: context.Background(),
				input: &model_cdk.CreateCDKBatchInput{
					ChannelID:        1001,
					Description:      "Test NO Limit",
					GenerationOption: model_cdk.RandomGeneration,
					GenerationCount:  2,
					StartTime:        time.Now().Unix(),
					EndTime:          time.Now().Add(24 * time.Hour).Unix(),
					CDKUseLimit:      -1,
					Rewards: []*commonPB.ItemBase{
						{ItemId: 101, ItemCount: 10000},
						{ItemId: 102, ItemCount: 100},
					},
				},
			},
			wantErr: false,
			checkFn: func(t *testing.T, output *model_cdk.CreateCDKBatchOutput, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, output)
				assert.NotNil(t, output.Batch)
				assert.Equal(t, int32(10), output.Batch.CDKCount)
				assert.Equal(t, model_cdk.RandomGeneration, model_cdk.CDKGenerationOption(output.Batch.GenerationOption))
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CreateCDKBatchWithCodes(tt.args.ctx, tt.args.input)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			if tt.checkFn != nil {
				tt.checkFn(t, got, err)
			}
		})
	}
}

// {
// 	name: "Success - Manual Input",
// 	args: args{
// 		ctx: context.Background(),
// 		input: &model_cdk.CreateCDKBatchInput{
// 			ChannelID:        2,
// 			Description:      "Test Batch Manual",
// 			GenerationOption: model_cdk.ManualInput,
// 			ManualCDKs:       []string{"MANUALCODE001", "MANUALCODE002", "MANUALCODE003"},
// 			StartTime:        time.Now().Unix(),
// 			EndTime:          time.Now().Add(48 * time.Hour).Unix(),
// 			CDKUseLimit:      -1,
// 			Rewards: []model_cdk.RewardItem{
// 				{ItemID: 2001, Count: 5},
// 			},
// 		},
// 	},
// 	wantErr: false,
// 	checkFn: func(t *testing.T, output *model_cdk.CreateCDKBatchOutput, err error) {
// 		assert.NoError(t, err)
// 		assert.NotNil(t, output)
// 		assert.NotNil(t, output.Batch)
// 		assert.Equal(t, int32(3), output.Batch.CDKCount)
// 		assert.Equal(t, model_cdk.ManualInput, model_cdk.CDKGenerationOption(output.Batch.GenerationOption))
// 	},
// },
// {
// 	name: "Fail - StartTime >= EndTime",
// 	args: args{
// 		ctx: context.Background(),
// 		input: &model_cdk.CreateCDKBatchInput{
// 			ChannelID:        1,
// 			Description:      "Invalid Time",
// 			GenerationOption: model_cdk.RandomGeneration,
// 			GenerationCount:  5,
// 			StartTime:        time.Now().Add(24 * time.Hour).Unix(),
// 			EndTime:          time.Now().Unix(),
// 			CDKUseLimit:      1,
// 			Rewards:          []model_cdk.RewardItem{{ItemID: 1, Count: 1}},
// 		},
// 	},
// 	wantErr: true,
// 	checkFn: func(t *testing.T, output *model_cdk.CreateCDKBatchOutput, err error) {
// 		assert.Error(t, err)
// 		assert.Nil(t, output)
// 		assert.Contains(t, err.Error(), "end_time must be greater than start_time")
// 	},
// },
// {
// 	name: "Fail - Empty Rewards",
// 	args: args{
// 		ctx: context.Background(),
// 		input: &model_cdk.CreateCDKBatchInput{
// 			ChannelID:        1,
// 			Description:      "No Rewards",
// 			GenerationOption: model_cdk.RandomGeneration,
// 			GenerationCount:  5,
// 			StartTime:        time.Now().Unix(),
// 			EndTime:          time.Now().Add(24 * time.Hour).Unix(),
// 			CDKUseLimit:      1,
// 			Rewards:          []model_cdk.RewardItem{},
// 		},
// 	},
// 	wantErr: true,
// 	checkFn: func(t *testing.T, output *model_cdk.CreateCDKBatchOutput, err error) {
// 		assert.Error(t, err)
// 		assert.Nil(t, output)
// 		assert.Contains(t, err.Error(), "rewards list cannot be empty")
// 	},
// },
// {
// 	name: "Fail - Random Generation Count <= 0",
// 	args: args{
// 		ctx: context.Background(),
// 		input: &model_cdk.CreateCDKBatchInput{
// 			ChannelID:        1,
// 			Description:      "Invalid Random Count",
// 			GenerationOption: model_cdk.RandomGeneration,
// 			GenerationCount:  0,
// 			StartTime:        time.Now().Unix(),
// 			EndTime:          time.Now().Add(24 * time.Hour).Unix(),
// 			CDKUseLimit:      1,
// 			Rewards:          []model_cdk.RewardItem{{ItemID: 1, Count: 1}},
// 		},
// 	},
// 	wantErr: true,
// 	checkFn: func(t *testing.T, output *model_cdk.CreateCDKBatchOutput, err error) {
// 		assert.Error(t, err)
// 		assert.Nil(t, output)
// 		assert.Contains(t, err.Error(), "generation_count must be > 0 for random generation")
// 	},
// },
// {
// 	name: "Fail - Random Generation Count > 100000",
// 	args: args{
// 		ctx: context.Background(),
// 		input: &model_cdk.CreateCDKBatchInput{
// 			ChannelID:        1,
// 			Description:      "Too Many Random",
// 			GenerationOption: model_cdk.RandomGeneration,
// 			GenerationCount:  100001,
// 			StartTime:        time.Now().Unix(),
// 			EndTime:          time.Now().Add(24 * time.Hour).Unix(),
// 			CDKUseLimit:      1,
// 			Rewards:          []model_cdk.RewardItem{{ItemID: 1, Count: 1}},
// 		},
// 	},
// 	wantErr: true,
// 	checkFn: func(t *testing.T, output *model_cdk.CreateCDKBatchOutput, err error) {
// 		assert.Error(t, err)
// 		assert.Nil(t, output)
// 		assert.Contains(t, err.Error(), "generation_count cannot be greater than 100000 for random generation")
// 	},
// },
// {
// 	name: "Fail - ManualCDKs Empty for Manual Input",
// 	args: args{
// 		ctx: context.Background(),
// 		input: &model_cdk.CreateCDKBatchInput{
// 			ChannelID:        1,
// 			Description:      "Empty Manual CDKs",
// 			GenerationOption: model_cdk.ManualInput,
// 			ManualCDKs:       []string{},
// 			StartTime:        time.Now().Unix(),
// 			EndTime:          time.Now().Add(24 * time.Hour).Unix(),
// 			CDKUseLimit:      1,
// 			Rewards:          []model_cdk.RewardItem{{ItemID: 1, Count: 1}},
// 		},
// 	},
// 	wantErr: true,
// 	checkFn: func(t *testing.T, output *model_cdk.CreateCDKBatchOutput, err error) {
// 		assert.Error(t, err)
// 		assert.Nil(t, output)
// 		assert.Contains(t, err.Error(), "manual_cdks cannot be empty for manual input")
// 	},
// },
// {
// 	name: "Fail - ManualCDKs Count > 100000",
// 	args: args{
// 		ctx: context.Background(),
// 		input: &model_cdk.CreateCDKBatchInput{
// 			ChannelID:        1,
// 			Description:      "Too Many Manual CDKs",
// 			GenerationOption: model_cdk.ManualInput,
// 			ManualCDKs:       make([]string, 100001),
// 			StartTime:        time.Now().Unix(),
// 			EndTime:          time.Now().Add(24 * time.Hour).Unix(),
// 			CDKUseLimit:      1,
// 			Rewards:          []model_cdk.RewardItem{{ItemID: 1, Count: 1}},
// 		},
// 	},
// 	wantErr: true,
// 	checkFn: func(t *testing.T, output *model_cdk.CreateCDKBatchOutput, err error) {
// 		assert.Error(t, err)
// 		assert.Nil(t, output)
// 		assert.Contains(t, err.Error(), "manual_cdks count cannot be greater than 100000")
// 	},
// },
// {
// 	name: "Fail - Invalid GenerationOption",
// 	args: args{
// 		ctx: context.Background(),
// 		input: &model_cdk.CreateCDKBatchInput{
// 			ChannelID:        1,
// 			Description:      "Invalid Option",
// 			GenerationOption: model_cdk.CDKGenerationOption(99),
// 			StartTime:        time.Now().Unix(),
// 			EndTime:          time.Now().Add(24 * time.Hour).Unix(),
// 			CDKUseLimit:      1,
// 			Rewards:          []model_cdk.RewardItem{{ItemID: 1, Count: 1}},
// 		},
// 	},
// 	wantErr: true,
// 	checkFn: func(t *testing.T, output *model_cdk.CreateCDKBatchOutput, err error) {
// 		assert.Error(t, err)
// 		assert.Nil(t, output)
// 		assert.Contains(t, err.Error(), "invalid GenerationOption: 99")
// 	},
// },
