package logic_cdk

import (
	"context"
	"errors"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"hallsrv/internal/dao/dao_cdk"
	"hallsrv/internal/model/model_cdk"
)

// CreateCDKBatchWithCodes 创建CDK批次和对应的CDK码
func CreateCDKBatchWithCodes(ctx context.Context, input *model_cdk.CreateCDKBatchInput) (*model_cdk.CreateCDKBatchOutput, error) {
	output := &model_cdk.CreateCDKBatchOutput{}
	entry := logx.NewLogEntry(ctx)
	entry.Infof("CreateCDKBatchWithCodes input: %+v", input)

	// 输入参数校验
	if input.StartTime >= input.EndTime {
		return nil, errors.New("end_time must be greater than start_time")
	}
	if len(input.Rewards) == 0 {
		return nil, errors.New("rewards list cannot be empty")
	}
	if input.GenerationOption == model_cdk.RandomGeneration && input.GenerationCount > 100000 {
		return nil, errors.New("generation_count cannot be greater than 100000 for random generation")
	}

	// 生成CDK
	var cdkCodesToCreate []string
	switch input.GenerationOption {
	case model_cdk.RandomGeneration:
		if input.GenerationCount <= 0 {
			return nil, errors.New("generation_count must be > 0 for random generation")
		}
		var genErr error
		cdkCodesToCreate, genErr = random.NewCDKeyGenerator(12).GenerateBatch(input.GenerationCount)
		if genErr != nil {
			return nil, fmt.Errorf("failed to generate CDK codes: %s", genErr)
		}
	case model_cdk.ManualInput:
		cdkCodesToCreate = input.ManualCDKs
	default:
		return nil, fmt.Errorf("invalid GenerationOption: %d", input.GenerationOption)
	}
	if len(cdkCodesToCreate) == 0 {
		return nil, errors.New("no CDK codes to create (count is zero or manual list is empty)")
	}

	// 创建CDKBatch对象
	cdkBatch := input.ToCDKBatch()
	cdkBatch.CDKCount = int32(len(cdkCodesToCreate))
	cdkRecords := model_cdk.CreateCDKRecords(cdkCodesToCreate)

	//  调用 DAO 层函数执行数据库操作
	err := dao_cdk.CreateCDKBatchWithRecords(ctx, cdkBatch, cdkRecords)
	if err != nil {
		entry.Errorf("CreateCDKBatchWithCodes: failed to create CDK batch and records via DAO: %v", err)
		return output, fmt.Errorf("failed to create CDK batch and associated codes: %w", err)
	}

	output.Batch = cdkBatch
	entry.Infof("CreateCDKBatchWithCodes: successfully created CDK batch ID %d with %d codes via DAO.", cdkBatch.ID, len(cdkCodesToCreate))
	return output, nil
}
